import React, { useState, useEffect } from 'react';
import {
  Bar<PERSON>hart3,
  TrendingUp,
  Pie<PERSON>hart,
  Calendar,
  MapPin,
  Users,
  Clock,
  Download,
  RefreshCw,
  Filter
} from 'lucide-react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
} from 'chart.js';
import { Bar, Pie, Line } from 'react-chartjs-2';
import { getSignalsStats, getSignals } from '../services/web/firestoreService';
import { getUserStats } from '../services/web/userService';
import { format, subDays, startOfDay, endOfDay } from 'date-fns';
import './Analytics.css';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement
);

const Analytics = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [dateRange, setDateRange] = useState('30'); // days
  const [analytics, setAnalytics] = useState({
    signalStats: {
      total: 0,
      pending: 0,
      inProgress: 0,
      resolved: 0
    },
    userStats: {
      total: 0,
      active: 0,
      admins: 0,
      collectors: 0
    },
    timeSeriesData: [],
    locationData: [],
    performanceData: []
  });

  useEffect(() => {
    loadAnalytics();
  }, [dateRange]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      setError('');

      const [signalStats, userStats, timeSeriesData] = await Promise.all([
        getSignalsStats(),
        getUserStats(),
        getTimeSeriesData()
      ]);

      setAnalytics({
        signalStats,
        userStats,
        timeSeriesData,
        locationData: await getLocationAnalytics(),
        performanceData: await getPerformanceAnalytics()
      });
    } catch (error) {
      console.error('Error loading analytics:', error);
      setError('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const getTimeSeriesData = async () => {
    try {
      const days = parseInt(dateRange);
      const endDate = new Date();
      const startDate = subDays(endDate, days);

      // Get signals for the date range
      const { signals } = await getSignals(1000, null, {});

      // Group signals by date
      const dateGroups = {};
      for (let i = 0; i < days; i++) {
        const date = subDays(endDate, i);
        const dateKey = format(date, 'yyyy-MM-dd');
        dateGroups[dateKey] = {
          date: dateKey,
          total: 0,
          pending: 0,
          inProgress: 0,
          resolved: 0
        };
      }

      signals.forEach(signal => {
        if (signal.createdAt) {
          const dateKey = format(signal.createdAt, 'yyyy-MM-dd');
          if (dateGroups[dateKey]) {
            dateGroups[dateKey].total++;
            dateGroups[dateKey][signal.status.replace('_', '')]++;
          }
        }
      });

      return Object.values(dateGroups).reverse();
    } catch (error) {
      console.error('Error getting time series data:', error);
      return [];
    }
  };

  const getLocationAnalytics = async () => {
    try {
      const { signals } = await getSignals(1000, null, {});

      // Group by approximate location (you might want to implement proper geocoding)
      const locationGroups = {};

      signals.forEach(signal => {
        if (signal.location && signal.location.latitude && signal.location.longitude) {
          // Round coordinates to create location groups
          const lat = Math.round(signal.location.latitude * 100) / 100;
          const lng = Math.round(signal.location.longitude * 100) / 100;
          const key = `${lat},${lng}`;

          if (!locationGroups[key]) {
            locationGroups[key] = {
              location: key,
              count: 0,
              resolved: 0,
              pending: 0
            };
          }

          locationGroups[key].count++;
          if (signal.status === 'resolved') {
            locationGroups[key].resolved++;
          } else if (signal.status === 'pending') {
            locationGroups[key].pending++;
          }
        }
      });

      return Object.values(locationGroups)
        .sort((a, b) => b.count - a.count)
        .slice(0, 10); // Top 10 locations
    } catch (error) {
      console.error('Error getting location analytics:', error);
      return [];
    }
  };

  const getPerformanceAnalytics = async () => {
    try {
      const { signals } = await getSignals(1000, null, {});

      // Calculate average resolution time
      const resolvedSignals = signals.filter(s => s.status === 'resolved' && s.resolvedAt && s.createdAt);

      const resolutionTimes = resolvedSignals.map(signal => {
        const created = signal.createdAt;
        const resolved = signal.resolvedAt;
        return (resolved - created) / (1000 * 60 * 60 * 24); // days
      });

      const avgResolutionTime = resolutionTimes.length > 0
        ? resolutionTimes.reduce((a, b) => a + b, 0) / resolutionTimes.length
        : 0;

      return {
        avgResolutionTime: Math.round(avgResolutionTime * 10) / 10,
        totalResolved: resolvedSignals.length,
        resolutionRate: signals.length > 0 ? (resolvedSignals.length / signals.length) * 100 : 0
      };
    } catch (error) {
      console.error('Error getting performance analytics:', error);
      return {
        avgResolutionTime: 0,
        totalResolved: 0,
        resolutionRate: 0
      };
    }
  };

  // Chart configurations
  const statusPieData = {
    labels: ['Pending', 'In Progress', 'Resolved'],
    datasets: [
      {
        data: [
          analytics.signalStats.pending,
          analytics.signalStats.inProgress,
          analytics.signalStats.resolved
        ],
        backgroundColor: ['#FF9800', '#2196F3', '#4CAF50'],
        borderWidth: 2,
        borderColor: '#fff'
      }
    ]
  };

  const timeSeriesData = {
    labels: analytics.timeSeriesData.map(item => format(new Date(item.date), 'MMM dd')),
    datasets: [
      {
        label: 'New Reports',
        data: analytics.timeSeriesData.map(item => item.total),
        borderColor: '#2E7D32',
        backgroundColor: 'rgba(46, 125, 50, 0.1)',
        tension: 0.4
      },
      {
        label: 'Resolved',
        data: analytics.timeSeriesData.map(item => item.resolved),
        borderColor: '#4CAF50',
        backgroundColor: 'rgba(76, 175, 80, 0.1)',
        tension: 0.4
      }
    ]
  };

  const locationBarData = {
    labels: analytics.locationData.map(item => `Location ${item.location.split(',')[0]}`),
    datasets: [
      {
        label: 'Total Reports',
        data: analytics.locationData.map(item => item.count),
        backgroundColor: '#2E7D32',
      },
      {
        label: 'Resolved',
        data: analytics.locationData.map(item => item.resolved),
        backgroundColor: '#4CAF50',
      }
    ]
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
    },
  };

  const exportData = () => {
    const data = {
      signalStats: analytics.signalStats,
      userStats: analytics.userStats,
      timeSeriesData: analytics.timeSeriesData,
      locationData: analytics.locationData,
      performanceData: analytics.performanceData,
      exportDate: new Date().toISOString(),
      dateRange: `${dateRange} days`
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `cleanspot-analytics-${format(new Date(), 'yyyy-MM-dd')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading analytics...</p>
      </div>
    );
  }

  return (
    <div className="analytics">
      {/* Page Header */}
      <div className="page-header">
        <div className="page-title">
          <BarChart3 size={24} />
          <h1>Analytics & Reporting</h1>
        </div>
        <div className="header-actions">
          <div className="filter-group">
            <Calendar size={16} />
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="filter-select"
            >
              <option value="7">Last 7 days</option>
              <option value="30">Last 30 days</option>
              <option value="90">Last 90 days</option>
              <option value="365">Last year</option>
            </select>
          </div>

          <button
            onClick={loadAnalytics}
            className="btn btn-outline"
            disabled={loading}
          >
            <RefreshCw size={16} />
            Refresh
          </button>

          <button
            onClick={exportData}
            className="btn btn-primary"
          >
            <Download size={16} />
            Export Data
          </button>
        </div>
      </div>

      {error && (
        <div className="error-message">
          <span>{error}</span>
          <button onClick={() => setError('')} className="error-close">×</button>
        </div>
      )}

      {/* Key Metrics */}
      <div className="metrics-grid">
        <div className="metric-card">
          <div className="metric-icon signals">
            <BarChart3 size={24} />
          </div>
          <div className="metric-content">
            <div className="metric-value">{analytics.signalStats.total}</div>
            <div className="metric-label">Total Reports</div>
            <div className="metric-change">
              {analytics.signalStats.resolved > 0 && (
                <span className="positive">
                  {Math.round((analytics.signalStats.resolved / analytics.signalStats.total) * 100)}% resolved
                </span>
              )}
            </div>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-icon users">
            <Users size={24} />
          </div>
          <div className="metric-content">
            <div className="metric-value">{analytics.userStats.active}</div>
            <div className="metric-label">Active Users</div>
            <div className="metric-change">
              <span>{analytics.userStats.collectors} collectors, {analytics.userStats.admins} admins</span>
            </div>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-icon performance">
            <Clock size={24} />
          </div>
          <div className="metric-content">
            <div className="metric-value">{analytics.performanceData.avgResolutionTime}d</div>
            <div className="metric-label">Avg Resolution Time</div>
            <div className="metric-change">
              <span className="positive">
                {Math.round(analytics.performanceData.resolutionRate)}% resolution rate
              </span>
            </div>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-icon location">
            <MapPin size={24} />
          </div>
          <div className="metric-content">
            <div className="metric-value">{analytics.locationData.length}</div>
            <div className="metric-label">Active Locations</div>
            <div className="metric-change">
              <span>Areas with reports</span>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="charts-grid">
        {/* Status Distribution */}
        <div className="chart-card">
          <div className="chart-header">
            <h3>Status Distribution</h3>
            <PieChart size={20} />
          </div>
          <div className="chart-container">
            <Pie data={statusPieData} options={chartOptions} />
          </div>
        </div>

        {/* Time Series */}
        <div className="chart-card wide">
          <div className="chart-header">
            <h3>Reports Over Time</h3>
            <TrendingUp size={20} />
          </div>
          <div className="chart-container">
            <Line data={timeSeriesData} options={chartOptions} />
          </div>
        </div>

        {/* Location Analytics */}
        <div className="chart-card wide">
          <div className="chart-header">
            <h3>Top Locations by Reports</h3>
            <MapPin size={20} />
          </div>
          <div className="chart-container">
            <Bar data={locationBarData} options={chartOptions} />
          </div>
        </div>
      </div>

      {/* Performance Summary */}
      <div className="performance-summary">
        <h3>Performance Summary</h3>
        <div className="summary-grid">
          <div className="summary-item">
            <div className="summary-label">Total Reports Resolved</div>
            <div className="summary-value">{analytics.performanceData.totalResolved}</div>
          </div>
          <div className="summary-item">
            <div className="summary-label">Average Resolution Time</div>
            <div className="summary-value">{analytics.performanceData.avgResolutionTime} days</div>
          </div>
          <div className="summary-item">
            <div className="summary-label">Resolution Rate</div>
            <div className="summary-value">{Math.round(analytics.performanceData.resolutionRate)}%</div>
          </div>
          <div className="summary-item">
            <div className="summary-label">Active Locations</div>
            <div className="summary-value">{analytics.locationData.length}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Analytics;
