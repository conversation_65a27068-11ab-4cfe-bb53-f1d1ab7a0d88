import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from 'react-leaflet';
import { getSignalsForMap, SIGNAL_STATUS, listenToSignals } from '../services/web/firestoreService';
import { Filter, RefreshCw, MapPin, Calendar, Layers, RotateCcw } from 'lucide-react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import './SignalMap.css';

// Fix for default markers in react-leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Custom marker icons for different statuses
const createCustomIcon = (status) => {
  const colors = {
    [SIGNAL_STATUS.PENDING]: '#FF9800',
    [SIGNAL_STATUS.IN_PROGRESS]: '#2196F3',
    [SIGNAL_STATUS.RESOLVED]: '#4CAF50'
  };
  
  return L.divIcon({
    className: 'custom-marker',
    html: `<div class="marker-pin" style="background-color: ${colors[status]}">
             <div class="marker-icon">📍</div>
           </div>`,
    iconSize: [30, 30],
    iconAnchor: [15, 30]
  });
};

// Map bounds update component
const MapBoundsUpdater = ({ signals }) => {
  const map = useMap();

  useEffect(() => {
    if (signals.length > 0) {
      const group = new L.featureGroup(
        signals.map(signal =>
          L.marker([signal.location.latitude, signal.location.longitude])
        )
      );
      map.fitBounds(group.getBounds().pad(0.1));
    }
  }, [signals, map]);

  return null;
};

const SignalMap = () => {
  const [signals, setSignals] = useState([]);
  const [filteredSignals, setFilteredSignals] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filters, setFilters] = useState({
    status: '',
    dateRange: 'all', // all, today, week, month
    showHeatMap: false
  });
  const [realTimeEnabled, setRealTimeEnabled] = useState(true);
  const mapRef = useRef();

  useEffect(() => {
    if (realTimeEnabled) {
      // Set up real-time listener
      const unsubscribe = listenToSignals((newSignals) => {
        setSignals(newSignals);
        setLoading(false);
      });
      return () => unsubscribe && unsubscribe();
    } else {
      loadSignals();
    }
  }, [realTimeEnabled]);

  useEffect(() => {
    // Apply filters to signals
    let filtered = [...signals];

    // Status filter
    if (filters.status) {
      filtered = filtered.filter(signal => signal.status === filters.status);
    }

    // Date range filter
    if (filters.dateRange !== 'all') {
      const now = new Date();
      const cutoffDate = new Date();

      switch (filters.dateRange) {
        case 'today':
          cutoffDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          cutoffDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          cutoffDate.setMonth(now.getMonth() - 1);
          break;
      }

      filtered = filtered.filter(signal =>
        signal.createdAt && signal.createdAt >= cutoffDate
      );
    }

    setFilteredSignals(filtered);
  }, [signals, filters]);

  const loadSignals = async () => {
    try {
      setLoading(true);
      const signalsData = await getSignalsForMap(filters);
      setSignals(signalsData);
    } catch (error) {
      console.error('Error loading signals for map:', error);
      setError('Failed to load signals');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const formatDate = (date) => {
    if (!date) return 'N/A';
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case SIGNAL_STATUS.PENDING:
        return '#FF9800';
      case SIGNAL_STATUS.IN_PROGRESS:
        return '#2196F3';
      case SIGNAL_STATUS.RESOLVED:
        return '#4CAF50';
      default:
        return '#757575';
    }
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading map...</p>
      </div>
    );
  }

  return (
    <div className="signal-map-page">
      {error && (
        <div className="error-message">
          <span>{error}</span>
          <button onClick={() => setError('')} className="error-close">×</button>
        </div>
      )}

      {/* Map Controls */}
      <div className="map-controls">
        <div className="controls-row">
          <div className="controls-left">
            <div className="filter-group">
              <Filter size={16} />
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className="filter-select"
              >
                <option value="">All Status</option>
                <option value={SIGNAL_STATUS.PENDING}>Pending</option>
                <option value={SIGNAL_STATUS.IN_PROGRESS}>In Progress</option>
                <option value={SIGNAL_STATUS.RESOLVED}>Resolved</option>
              </select>
            </div>

            <div className="filter-group">
              <Calendar size={16} />
              <select
                value={filters.dateRange}
                onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                className="filter-select"
              >
                <option value="all">All Time</option>
                <option value="today">Today</option>
                <option value="week">Last Week</option>
                <option value="month">Last Month</option>
              </select>
            </div>

            <div className="toggle-group">
              <label className="toggle-label">
                <input
                  type="checkbox"
                  checked={realTimeEnabled}
                  onChange={(e) => setRealTimeEnabled(e.target.checked)}
                  className="toggle-input"
                />
                <span className="toggle-text">Real-time Updates</span>
              </label>
            </div>
          </div>

          <div className="controls-right">
            <div className="signal-count">
              <MapPin size={16} />
              <span>{filteredSignals.length} of {signals.length} signals</span>
            </div>

            <button
              onClick={loadSignals}
              className="btn btn-outline refresh-btn"
              disabled={loading || realTimeEnabled}
              title={realTimeEnabled ? "Disable real-time to manually refresh" : "Refresh map data"}
            >
              <RefreshCw size={16} />
              Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Legend */}
      <div className="map-legend">
        <h4>Legend</h4>
        <div className="legend-items">
          <div className="legend-item">
            <div 
              className="legend-color" 
              style={{ backgroundColor: getStatusColor(SIGNAL_STATUS.PENDING) }}
            ></div>
            <span>Pending</span>
          </div>
          <div className="legend-item">
            <div 
              className="legend-color" 
              style={{ backgroundColor: getStatusColor(SIGNAL_STATUS.IN_PROGRESS) }}
            ></div>
            <span>In Progress</span>
          </div>
          <div className="legend-item">
            <div 
              className="legend-color" 
              style={{ backgroundColor: getStatusColor(SIGNAL_STATUS.RESOLVED) }}
            ></div>
            <span>Resolved</span>
          </div>
        </div>
      </div>

      {/* Map Container */}
      <div className="map-container">
        <MapContainer
          center={[14.0583, -87.2073]} // Default to Honduras coordinates
          zoom={8}
          style={{ height: '100%', width: '100%' }}
        >
          <TileLayer
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          />
          
          <MapBoundsUpdater signals={filteredSignals} />

          {filteredSignals.map((signal) => (
            <Marker
              key={signal.id}
              position={[signal.location.latitude, signal.location.longitude]}
              icon={createCustomIcon(signal.status)}
            >
              <Popup>
                <div className="popup-content">
                  <div className="popup-header">
                    <h4>Environmental Report</h4>
                    <span 
                      className={`status-badge status-${signal.status.replace('_', '-')}`}
                      style={{ backgroundColor: getStatusColor(signal.status) }}
                    >
                      {signal.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </div>
                  
                  <div className="popup-body">
                    <p className="popup-description">
                      {signal.description.length > 100 
                        ? `${signal.description.substring(0, 100)}...`
                        : signal.description
                      }
                    </p>
                    
                    <div className="popup-details">
                      <p><strong>Reporter:</strong> {signal.userDisplayName}</p>
                      <p><strong>Created:</strong> {formatDate(signal.createdAt)}</p>
                      {signal.location.address && (
                        <p><strong>Address:</strong> {signal.location.address}</p>
                      )}
                    </div>
                    
                    {signal.imageUrl && (
                      <div className="popup-image">
                        <img 
                          src={signal.imageUrl} 
                          alt="Environmental issue" 
                          style={{ width: '100%', maxHeight: '150px', objectFit: 'cover', borderRadius: '4px' }}
                        />
                      </div>
                    )}
                  </div>
                </div>
              </Popup>
            </Marker>
          ))}
        </MapContainer>
      </div>
    </div>
  );
};

export default SignalMap;
