import React, { useState } from 'react';
import { X, Eye, EyeOff, User, Mail, Lock, Shield } from 'lucide-react';
import { USER_ROLES } from '../services/web/authService';
import './UserModals.css';

export const CreateUserModal = ({ isOpen, onClose, onSubmit, form, loading }) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  if (!isOpen) return null;

  const { register, handleSubmit, formState: { errors }, watch } = form;

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>Add New User</h2>
          <button className="modal-close" onClick={onClose}>
            <X size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="modal-body">
          <div className="form-group">
            <label className="form-label">
              <Mail size={16} />
              Email Address
            </label>
            <input
              type="email"
              {...register('email')}
              className={`form-input ${errors.email ? 'error' : ''}`}
              placeholder="Enter email address"
              disabled={loading}
            />
            {errors.email && (
              <span className="error-text">{errors.email.message}</span>
            )}
          </div>

          <div className="form-group">
            <label className="form-label">
              <User size={16} />
              Display Name
            </label>
            <input
              type="text"
              {...register('displayName')}
              className={`form-input ${errors.displayName ? 'error' : ''}`}
              placeholder="Enter full name"
              disabled={loading}
            />
            {errors.displayName && (
              <span className="error-text">{errors.displayName.message}</span>
            )}
          </div>

          <div className="form-group">
            <label className="form-label">
              <Lock size={16} />
              Password
            </label>
            <div className="password-input-container">
              <input
                type={showPassword ? 'text' : 'password'}
                {...register('password')}
                className={`form-input ${errors.password ? 'error' : ''}`}
                placeholder="Enter password"
                disabled={loading}
              />
              <button
                type="button"
                className="password-toggle"
                onClick={() => setShowPassword(!showPassword)}
                disabled={loading}
              >
                {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
              </button>
            </div>
            {errors.password && (
              <span className="error-text">{errors.password.message}</span>
            )}
          </div>

          <div className="form-group">
            <label className="form-label">
              <Lock size={16} />
              Confirm Password
            </label>
            <div className="password-input-container">
              <input
                type={showConfirmPassword ? 'text' : 'password'}
                {...register('confirmPassword')}
                className={`form-input ${errors.confirmPassword ? 'error' : ''}`}
                placeholder="Confirm password"
                disabled={loading}
              />
              <button
                type="button"
                className="password-toggle"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                disabled={loading}
              >
                {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}
              </button>
            </div>
            {errors.confirmPassword && (
              <span className="error-text">{errors.confirmPassword.message}</span>
            )}
          </div>

          <div className="form-group">
            <label className="form-label">
              <Shield size={16} />
              Role
            </label>
            <select
              {...register('role')}
              className={`form-select ${errors.role ? 'error' : ''}`}
              disabled={loading}
            >
              <option value={USER_ROLES.COLLECTOR}>Waste Collector</option>
              <option value={USER_ROLES.ADMIN}>Administrator</option>
            </select>
            {errors.role && (
              <span className="error-text">{errors.role.message}</span>
            )}
          </div>

          <div className="modal-footer">
            <button 
              type="button" 
              className="btn btn-outline" 
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </button>
            <button 
              type="submit" 
              className="btn btn-primary"
              disabled={loading}
            >
              {loading ? (
                <>
                  <div className="loading-spinner small"></div>
                  Creating...
                </>
              ) : (
                'Create User'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export const EditUserModal = ({ isOpen, onClose, onSubmit, form, user, loading }) => {
  if (!isOpen || !user) return null;

  const { register, handleSubmit, formState: { errors } } = form;

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>Edit User</h2>
          <button className="modal-close" onClick={onClose}>
            <X size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="modal-body">
          <div className="form-group">
            <label className="form-label">
              <Mail size={16} />
              Email Address
            </label>
            <input
              type="email"
              value={user.email}
              className="form-input"
              disabled
              readOnly
            />
            <span className="help-text">Email cannot be changed</span>
          </div>

          <div className="form-group">
            <label className="form-label">
              <User size={16} />
              Display Name
            </label>
            <input
              type="text"
              {...register('displayName')}
              className={`form-input ${errors.displayName ? 'error' : ''}`}
              placeholder="Enter full name"
              disabled={loading}
            />
            {errors.displayName && (
              <span className="error-text">{errors.displayName.message}</span>
            )}
          </div>

          <div className="form-group">
            <label className="form-label">
              <Shield size={16} />
              Role
            </label>
            <select
              {...register('role')}
              className={`form-select ${errors.role ? 'error' : ''}`}
              disabled={loading}
            >
              <option value={USER_ROLES.COLLECTOR}>Waste Collector</option>
              <option value={USER_ROLES.ADMIN}>Administrator</option>
            </select>
            {errors.role && (
              <span className="error-text">{errors.role.message}</span>
            )}
          </div>

          <div className="modal-footer">
            <button 
              type="button" 
              className="btn btn-outline" 
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </button>
            <button 
              type="submit" 
              className="btn btn-primary"
              disabled={loading}
            >
              {loading ? (
                <>
                  <div className="loading-spinner small"></div>
                  Updating...
                </>
              ) : (
                'Update User'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
