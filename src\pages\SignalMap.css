.signal-map-page {
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
  position: relative;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background-color: #ffeaea;
  border: 1px solid #ffcdd2;
  border-radius: 8px;
  color: var(--error-color);
  margin-bottom: 16px;
  position: relative;
}

.error-close {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: var(--error-color);
}

.map-controls {
  background: var(--surface-color);
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.controls-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.controls-left {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.controls-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-select {
  padding: 8px 12px;
  border: 2px solid var(--border-color);
  border-radius: 6px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  min-width: 140px;
}

.refresh-btn {
  white-space: nowrap;
}

.toggle-group {
  display: flex;
  align-items: center;
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: var(--text-primary);
}

.toggle-input {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.toggle-text {
  user-select: none;
}

.signal-count {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary);
  font-size: 14px;
}

.map-legend {
  position: absolute;
  top: 80px;
  right: 20px;
  background: var(--surface-color);
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 150px;
}

.map-legend h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.legend-item span {
  font-size: 12px;
  color: var(--text-primary);
}

.map-container {
  flex: 1;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Custom Marker Styles */
.custom-marker {
  background: transparent;
  border: none;
}

.marker-pin {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  border: 3px solid white;
  position: relative;
}

.marker-pin::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid inherit;
}

.marker-icon {
  font-size: 14px;
  color: white;
}

/* Popup Styles */
.popup-content {
  min-width: 250px;
  max-width: 300px;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color);
}

.popup-header h4 {
  margin: 0;
  font-size: 16px;
  color: var(--text-primary);
}

.status-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
}

.popup-body {
  font-size: 14px;
}

.popup-description {
  margin: 0 0 12px 0;
  line-height: 1.4;
  color: var(--text-primary);
}

.popup-details {
  margin-bottom: 12px;
}

.popup-details p {
  margin: 4px 0;
  color: var(--text-secondary);
}

.popup-details strong {
  color: var(--text-primary);
}

.popup-image {
  margin-top: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .signal-map-page {
    height: calc(100vh - 100px);
  }

  .controls-row {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .controls-left {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .controls-right {
    justify-content: space-between;
  }

  .filter-group {
    justify-content: space-between;
  }
  
  .map-legend {
    position: relative;
    top: auto;
    right: auto;
    margin-bottom: 16px;
    align-self: flex-end;
  }
  
  .popup-content {
    min-width: 200px;
    max-width: 250px;
  }
}

@media (max-width: 480px) {
  .map-controls {
    padding: 12px 16px;
  }
  
  .controls-left {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .filter-group {
    justify-content: space-between;
  }
  
  .map-legend {
    width: 100%;
  }
  
  .legend-items {
    flex-direction: row;
    justify-content: space-around;
  }
}
