.user-management {
  max-width: 1400px;
  margin: 0 auto;
}

.error-message,
.success-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  position: relative;
}

.error-message {
  background-color: #ffeaea;
  border: 1px solid #ffcdd2;
  color: var(--error-color);
}

.success-message {
  background-color: #e8f5e8;
  border: 1px solid #c8e6c9;
  color: var(--success-color);
}

.error-close,
.success-close {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
}

/* User Stats */
.user-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: var(--surface-color);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.total {
  background: var(--primary-color);
}

.stat-icon.active {
  background: var(--success-color);
}

.stat-icon.admin {
  background: var(--info-color);
}

.stat-icon.collector {
  background: var(--warning-color);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
  margin-top: 4px;
}

/* User Filters */
.user-filters {
  background: var(--surface-color);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filters-row {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.search-box svg {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
}

.search-input {
  width: 100%;
  padding: 10px 12px 10px 40px;
  border: 2px solid var(--border-color);
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-select {
  padding: 10px 12px;
  border: 2px solid var(--border-color);
  border-radius: 6px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  min-width: 140px;
}

.toggle-group {
  display: flex;
  align-items: center;
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: var(--text-primary);
}

.toggle-input {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.toggle-text {
  user-select: none;
}

.refresh-btn {
  white-space: nowrap;
}

/* Users Table */
.users-table-container {
  background: var(--surface-color);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color);
}

.table-header h2 {
  margin: 0;
  color: var(--text-primary);
  font-size: 20px;
  font-weight: 600;
}

.table-count {
  color: var(--text-secondary);
  font-size: 14px;
}

.table-responsive {
  overflow-x: auto;
}

.users-table table {
  width: 100%;
  border-collapse: collapse;
}

.users-table th {
  background: var(--background-color);
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
  border-bottom: 1px solid var(--border-color);
  white-space: nowrap;
}

.users-table td {
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  vertical-align: middle;
}

.users-table tr:hover {
  background-color: var(--background-color);
}

/* Table Cell Styles */
.user-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.user-email {
  font-size: 14px;
  color: var(--text-secondary);
}

.role-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.role-badge.role-admin {
  background: #e3f2fd;
  color: #1976d2;
}

.role-badge.role-collector {
  background: #fff3e0;
  color: #f57c00;
}

.status-cell {
  display: flex;
  align-items: center;
}

.status-toggle {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: none;
  border-radius: 16px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.status-toggle.active {
  background: #e8f5e8;
  color: var(--success-color);
}

.status-toggle.inactive {
  background: #ffeaea;
  color: var(--error-color);
}

.date-cell {
  font-size: 14px;
  color: var(--text-secondary);
}

.actions-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-btn {
  padding: 6px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-btn {
  background: var(--info-color);
  color: white;
}

.edit-btn:hover {
  background: #1976d2;
}

.reset-btn {
  background: var(--warning-color);
  color: white;
}

.reset-btn:hover {
  background: #f57c00;
}

.delete-btn {
  background: var(--error-color);
  color: white;
}

.delete-btn:hover {
  background: #d32f2f;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-secondary);
}

.empty-state svg {
  margin-bottom: 16px;
  color: var(--text-secondary);
}

.empty-state h3 {
  margin: 0 0 8px 0;
  color: var(--text-primary);
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .user-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  .filters-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .search-box {
    min-width: auto;
  }
  
  .filter-group {
    justify-content: space-between;
  }
  
  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .users-table th,
  .users-table td {
    padding: 8px 12px;
  }
  
  .actions-cell {
    flex-direction: column;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .user-stats {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    padding: 16px;
  }
  
  .user-filters {
    padding: 16px;
  }
}
