import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {
  Users,
  UserPlus,
  Edit3,
  Trash2,
  Mail,
  Shield,
  ShieldCheck,
  ToggleLeft,
  ToggleRight,
  Search,
  Filter,
  RefreshCw,
  Eye,
  EyeOff,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import {
  getWebUsers,
  createWebUser,
  updateWebUser,
  toggleUserStatus,
  deleteWebUser,
  sendUserPasswordReset,
  getUserStats,
  listenToWebUsers
} from '../services/web/userService';
import { USER_ROLES } from '../services/web/authService';
import { useAuth } from '../contexts/AuthContext';
import { CreateUserModal, EditUserModal } from '../components/UserModals';
import './UserManagement.css';

// Validation schema for user creation
const createUserSchema = yup.object({
  email: yup
    .string()
    .email('Invalid email address')
    .required('Email is required'),
  displayName: yup
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(50, 'Name must be less than 50 characters')
    .required('Display name is required'),
  password: yup
    .string()
    .min(6, 'Password must be at least 6 characters')
    .required('Password is required'),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref('password'), null], 'Passwords must match')
    .required('Please confirm password'),
  role: yup
    .string()
    .oneOf([USER_ROLES.ADMIN, USER_ROLES.COLLECTOR], 'Invalid role')
    .required('Role is required')
});

// Validation schema for user editing
const editUserSchema = yup.object({
  displayName: yup
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(50, 'Name must be less than 50 characters')
    .required('Display name is required'),
  role: yup
    .string()
    .oneOf([USER_ROLES.ADMIN, USER_ROLES.COLLECTOR], 'Invalid role')
    .required('Role is required')
});

const UserManagement = () => {
  const { user: currentUser } = useAuth();
  const [users, setUsers] = useState([]);
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    admins: 0,
    collectors: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [filters, setFilters] = useState({
    search: '',
    role: '',
    status: ''
  });
  const [realTimeEnabled, setRealTimeEnabled] = useState(true);

  // Form for creating users
  const createForm = useForm({
    resolver: yupResolver(createUserSchema),
    defaultValues: {
      email: '',
      displayName: '',
      password: '',
      confirmPassword: '',
      role: USER_ROLES.COLLECTOR
    }
  });

  // Form for editing users
  const editForm = useForm({
    resolver: yupResolver(editUserSchema)
  });

  useEffect(() => {
    if (realTimeEnabled) {
      // Set up real-time listener
      const unsubscribe = listenToWebUsers((newUsers) => {
        setUsers(newUsers);
        setLoading(false);
      });
      return () => unsubscribe && unsubscribe();
    } else {
      loadUsers();
    }
  }, [realTimeEnabled]);

  useEffect(() => {
    loadStats();
  }, [users]);

  const loadUsers = async () => {
    try {
      setLoading(true);
      const usersData = await getWebUsers();
      setUsers(usersData);
    } catch (error) {
      console.error('Error loading users:', error);
      setError('Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const statsData = await getUserStats();
      setStats(statsData);
    } catch (error) {
      console.error('Error loading user stats:', error);
    }
  };

  const handleCreateUser = async (data) => {
    try {
      setError('');
      setSuccess('');
      await createWebUser(data);
      setSuccess('User created successfully');
      setShowCreateModal(false);
      createForm.reset();
      if (!realTimeEnabled) {
        loadUsers();
      }
      loadStats();
    } catch (error) {
      console.error('Error creating user:', error);
      setError(error.message || 'Failed to create user');
    }
  };

  const handleEditUser = async (data) => {
    try {
      setError('');
      setSuccess('');
      await updateWebUser(editingUser.id, data);
      setSuccess('User updated successfully');
      setEditingUser(null);
      editForm.reset();
      if (!realTimeEnabled) {
        loadUsers();
      }
      loadStats();
    } catch (error) {
      console.error('Error updating user:', error);
      setError(error.message || 'Failed to update user');
    }
  };

  const handleToggleStatus = async (userId, currentStatus) => {
    try {
      setError('');
      await toggleUserStatus(userId, !currentStatus);
      setSuccess(`User ${!currentStatus ? 'activated' : 'deactivated'} successfully`);
      if (!realTimeEnabled) {
        loadUsers();
      }
      loadStats();
    } catch (error) {
      console.error('Error toggling user status:', error);
      setError('Failed to update user status');
    }
  };

  const handleDeleteUser = async (userId) => {
    if (!window.confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
      return;
    }

    try {
      setError('');
      await deleteWebUser(userId);
      setSuccess('User deleted successfully');
      if (!realTimeEnabled) {
        loadUsers();
      }
      loadStats();
    } catch (error) {
      console.error('Error deleting user:', error);
      setError('Failed to delete user');
    }
  };

  const handleSendPasswordReset = async (email) => {
    try {
      setError('');
      await sendUserPasswordReset(email);
      setSuccess(`Password reset email sent to ${email}`);
    } catch (error) {
      console.error('Error sending password reset:', error);
      setError('Failed to send password reset email');
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const openEditModal = (user) => {
    setEditingUser(user);
    editForm.reset({
      displayName: user.displayName,
      role: user.role
    });
  };

  const closeModals = () => {
    setShowCreateModal(false);
    setEditingUser(null);
    createForm.reset();
    editForm.reset();
    setError('');
    setSuccess('');
  };

  // Filter users based on search and filters
  const filteredUsers = users.filter(user => {
    const matchesSearch = !filters.search ||
      user.displayName.toLowerCase().includes(filters.search.toLowerCase()) ||
      user.email.toLowerCase().includes(filters.search.toLowerCase());

    const matchesRole = !filters.role || user.role === filters.role;

    const matchesStatus = !filters.status ||
      (filters.status === 'active' && user.isActive) ||
      (filters.status === 'inactive' && !user.isActive);

    return matchesSearch && matchesRole && matchesStatus;
  });

  const formatDate = (date) => {
    if (!date) return 'Never';
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const getRoleIcon = (role) => {
    return role === USER_ROLES.ADMIN ? <ShieldCheck size={16} /> : <Shield size={16} />;
  };

  const getRoleColor = (role) => {
    return role === USER_ROLES.ADMIN ? 'role-admin' : 'role-collector';
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading users...</p>
      </div>
    );
  }

  return (
    <div className="user-management">
      {/* Page Header */}
      <div className="page-header">
        <div className="page-title">
          <Users size={24} />
          <h1>User Management</h1>
        </div>
        <button
          className="btn btn-primary"
          onClick={() => setShowCreateModal(true)}
        >
          <UserPlus size={16} />
          Add User
        </button>
      </div>

      {/* Messages */}
      {error && (
        <div className="error-message">
          <AlertCircle size={16} />
          <span>{error}</span>
          <button onClick={() => setError('')} className="error-close">×</button>
        </div>
      )}

      {success && (
        <div className="success-message">
          <CheckCircle size={16} />
          <span>{success}</span>
          <button onClick={() => setSuccess('')} className="success-close">×</button>
        </div>
      )}

      {/* Stats Cards */}
      <div className="user-stats">
        <div className="stat-card">
          <div className="stat-icon total">
            <Users size={24} />
          </div>
          <div className="stat-content">
            <div className="stat-value">{stats.total}</div>
            <div className="stat-label">Total Users</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon active">
            <CheckCircle size={24} />
          </div>
          <div className="stat-content">
            <div className="stat-value">{stats.active}</div>
            <div className="stat-label">Active Users</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon admin">
            <ShieldCheck size={24} />
          </div>
          <div className="stat-content">
            <div className="stat-value">{stats.admins}</div>
            <div className="stat-label">Administrators</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon collector">
            <Shield size={24} />
          </div>
          <div className="stat-content">
            <div className="stat-value">{stats.collectors}</div>
            <div className="stat-label">Collectors</div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="user-filters">
        <div className="filters-row">
          <div className="search-box">
            <Search size={16} />
            <input
              type="text"
              placeholder="Search users..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="search-input"
            />
          </div>

          <div className="filter-group">
            <Filter size={16} />
            <select
              value={filters.role}
              onChange={(e) => handleFilterChange('role', e.target.value)}
              className="filter-select"
            >
              <option value="">All Roles</option>
              <option value={USER_ROLES.ADMIN}>Administrator</option>
              <option value={USER_ROLES.COLLECTOR}>Collector</option>
            </select>
          </div>

          <div className="filter-group">
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="filter-select"
            >
              <option value="">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>

          <div className="toggle-group">
            <label className="toggle-label">
              <input
                type="checkbox"
                checked={realTimeEnabled}
                onChange={(e) => setRealTimeEnabled(e.target.checked)}
                className="toggle-input"
              />
              <span className="toggle-text">Real-time</span>
            </label>
          </div>

          <button
            onClick={loadUsers}
            className="btn btn-outline refresh-btn"
            disabled={loading || realTimeEnabled}
          >
            <RefreshCw size={16} />
            Refresh
          </button>
        </div>
      </div>

      {/* Users Table */}
      <div className="users-table-container">
        <div className="table-header">
          <h2>Users</h2>
          <span className="table-count">
            {filteredUsers.length} of {users.length} users
          </span>
        </div>

        <div className="users-table">
          {filteredUsers.length === 0 ? (
            <div className="empty-state">
              <Users size={48} />
              <h3>No users found</h3>
              <p>No users match your current filters.</p>
            </div>
          ) : (
            <div className="table-responsive">
              <table>
                <thead>
                  <tr>
                    <th>User</th>
                    <th>Role</th>
                    <th>Status</th>
                    <th>Created</th>
                    <th>Last Login</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredUsers.map((user) => (
                    <tr key={user.id}>
                      <td>
                        <div className="user-cell">
                          <div className="user-avatar">
                            {user.displayName.charAt(0).toUpperCase()}
                          </div>
                          <div className="user-info">
                            <div className="user-name">{user.displayName}</div>
                            <div className="user-email">{user.email}</div>
                          </div>
                        </div>
                      </td>
                      <td>
                        <div className={`role-badge ${getRoleColor(user.role)}`}>
                          {getRoleIcon(user.role)}
                          <span>{user.role}</span>
                        </div>
                      </td>
                      <td>
                        <div className="status-cell">
                          <button
                            onClick={() => handleToggleStatus(user.id, user.isActive)}
                            className={`status-toggle ${user.isActive ? 'active' : 'inactive'}`}
                            title={`Click to ${user.isActive ? 'deactivate' : 'activate'}`}
                          >
                            {user.isActive ? <ToggleRight size={20} /> : <ToggleLeft size={20} />}
                            <span>{user.isActive ? 'Active' : 'Inactive'}</span>
                          </button>
                        </div>
                      </td>
                      <td>
                        <div className="date-cell">
                          {formatDate(user.createdAt)}
                        </div>
                      </td>
                      <td>
                        <div className="date-cell">
                          {formatDate(user.lastLogin)}
                        </div>
                      </td>
                      <td>
                        <div className="actions-cell">
                          <button
                            onClick={() => openEditModal(user)}
                            className="action-btn edit-btn"
                            title="Edit User"
                          >
                            <Edit3 size={14} />
                          </button>

                          <button
                            onClick={() => handleSendPasswordReset(user.email)}
                            className="action-btn reset-btn"
                            title="Send Password Reset"
                          >
                            <Mail size={14} />
                          </button>

                          {user.id !== currentUser?.uid && (
                            <button
                              onClick={() => handleDeleteUser(user.id)}
                              className="action-btn delete-btn"
                              title="Delete User"
                            >
                              <Trash2 size={14} />
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Modals */}
      <CreateUserModal
        isOpen={showCreateModal}
        onClose={closeModals}
        onSubmit={handleCreateUser}
        form={createForm}
        loading={loading}
      />

      <EditUserModal
        isOpen={!!editingUser}
        onClose={closeModals}
        onSubmit={handleEditUser}
        form={editForm}
        user={editingUser}
        loading={loading}
      />
    </div>
  );
};
