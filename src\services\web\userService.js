import { 
  collection, 
  doc, 
  getDocs, 
  getDoc,
  setDoc,
  updateDoc, 
  deleteDoc,
  query, 
  orderBy,
  where,
  onSnapshot
} from 'firebase/firestore';
import { 
  createUserWithEmailAndPassword,
  updateProfile,
  sendPasswordResetEmail,
  deleteUser
} from 'firebase/auth';
import { db, auth } from './firebaseConfig';
import { USER_ROLES } from './authService';

// Get all web users
export const getWebUsers = async () => {
  try {
    const q = query(collection(db, 'webUsers'), orderBy('createdAt', 'desc'));
    const querySnapshot = await getDocs(q);
    const users = [];
    
    querySnapshot.forEach((doc) => {
      users.push({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        lastLogin: doc.data().lastLogin?.toDate()
      });
    });
    
    return users;
  } catch (error) {
    console.error('Error fetching web users:', error);
    throw error;
  }
};

// Get single user by ID
export const getWebUser = async (userId) => {
  try {
    const docRef = doc(db, 'webUsers', userId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      const data = docSnap.data();
      return {
        id: docSnap.id,
        ...data,
        createdAt: data.createdAt?.toDate(),
        lastLogin: data.lastLogin?.toDate()
      };
    } else {
      throw new Error('User not found');
    }
  } catch (error) {
    console.error('Error fetching user:', error);
    throw error;
  }
};

// Create new web user
export const createWebUser = async (userData) => {
  try {
    const { email, password, displayName, role } = userData;
    
    // Create user in Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;
    
    // Update user profile
    await updateProfile(user, {
      displayName: displayName
    });
    
    // Get default permissions based on role
    const permissions = getDefaultPermissions(role);
    
    // Create user document in webUsers collection
    await setDoc(doc(db, 'webUsers', user.uid), {
      email: email,
      displayName: displayName,
      role: role,
      permissions: permissions,
      createdAt: new Date(),
      lastLogin: null,
      isActive: true,
      createdBy: auth.currentUser?.uid
    });
    
    return {
      id: user.uid,
      email: email,
      displayName: displayName,
      role: role,
      permissions: permissions,
      isActive: true,
      createdAt: new Date()
    };
  } catch (error) {
    console.error('Error creating web user:', error);
    throw error;
  }
};

// Update web user
export const updateWebUser = async (userId, updateData) => {
  try {
    const docRef = doc(db, 'webUsers', userId);
    
    // If role is being updated, update permissions too
    if (updateData.role) {
      updateData.permissions = getDefaultPermissions(updateData.role);
    }
    
    updateData.updatedAt = new Date();
    
    await updateDoc(docRef, updateData);
    
    return true;
  } catch (error) {
    console.error('Error updating web user:', error);
    throw error;
  }
};

// Toggle user active status
export const toggleUserStatus = async (userId, isActive) => {
  try {
    const docRef = doc(db, 'webUsers', userId);
    await updateDoc(docRef, {
      isActive: isActive,
      updatedAt: new Date()
    });
    
    return true;
  } catch (error) {
    console.error('Error toggling user status:', error);
    throw error;
  }
};

// Delete web user
export const deleteWebUser = async (userId) => {
  try {
    // Delete from webUsers collection
    await deleteDoc(doc(db, 'webUsers', userId));
    
    // Note: Deleting from Firebase Auth requires admin SDK
    // For now, we just deactivate the user
    
    return true;
  } catch (error) {
    console.error('Error deleting web user:', error);
    throw error;
  }
};

// Send password reset email
export const sendUserPasswordReset = async (email) => {
  try {
    await sendPasswordResetEmail(auth, email);
    return true;
  } catch (error) {
    console.error('Error sending password reset:', error);
    throw error;
  }
};

// Listen to users changes (real-time)
export const listenToWebUsers = (callback) => {
  try {
    const q = query(collection(db, 'webUsers'), orderBy('createdAt', 'desc'));
    
    return onSnapshot(q, (querySnapshot) => {
      const users = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        users.push({
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate(),
          lastLogin: data.lastLogin?.toDate()
        });
      });
      callback(users);
    });
  } catch (error) {
    console.error('Error setting up users listener:', error);
    throw error;
  }
};

// Get default permissions based on role
const getDefaultPermissions = (role) => {
  switch (role) {
    case USER_ROLES.ADMIN:
      return [
        'view_signals',
        'update_signals',
        'delete_signals',
        'manage_users',
        'view_analytics',
        'export_data'
      ];
    case USER_ROLES.COLLECTOR:
      return [
        'view_signals',
        'update_signals',
        'view_analytics'
      ];
    default:
      return ['view_signals'];
  }
};

// Get user statistics
export const getUserStats = async () => {
  try {
    const usersRef = collection(db, 'webUsers');
    
    // Get all users
    const allUsersQuery = query(usersRef);
    const allUsersSnapshot = await getDocs(allUsersQuery);
    
    // Get active users
    const activeQuery = query(usersRef, where('isActive', '==', true));
    const activeSnapshot = await getDocs(activeQuery);
    
    // Get admins
    const adminQuery = query(usersRef, where('role', '==', USER_ROLES.ADMIN));
    const adminSnapshot = await getDocs(adminQuery);
    
    // Get collectors
    const collectorQuery = query(usersRef, where('role', '==', USER_ROLES.COLLECTOR));
    const collectorSnapshot = await getDocs(collectorQuery);
    
    return {
      total: allUsersSnapshot.size,
      active: activeSnapshot.size,
      inactive: allUsersSnapshot.size - activeSnapshot.size,
      admins: adminSnapshot.size,
      collectors: collectorSnapshot.size
    };
  } catch (error) {
    console.error('Error fetching user stats:', error);
    throw error;
  }
};
